#!/usr/bin/env python3
"""
Script pour vérifier la date de modification d'une page web
URL cible: https://www.louveinvest.com/scpi/scpi-a-credit#Les-solutions-d-investissement-en-SCPI-a-credit-de-Louve-Invest
"""

import requests
from lxml import html
import sys
from datetime import datetime
import time


class PageModificationChecker:
    def __init__(self, url):
        self.url = url
        self.xpath_relative = '//*[@id="w-node-_51261b10-05e6-ec0e-eacf-759532d9240a-52bf28c3"]/div/div/div[2]/p'
        self.xpath_absolute = '/html/body/div[2]/div[4]/div[1]/div[1]/div/div/div/div[1]/div/div/div[2]/p'
        self.class_selector = 'text-date-blog'

    def fetch_page(self):
        """Récupère le contenu de la page web"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(self.url, headers=headers, timeout=30)
            response.raise_for_status()
            return response.text
        except requests.exceptions.RequestException as e:
            print(f"Erreur lors de la récupération de la page: {e}")
            return None

    def parse_date(self, html_content):
        """Parse le HTML et extrait la date de modification"""
        try:
            tree = html.fromstring(html_content)

            # Essayer d'abord avec le XPath relatif
            date_elements = tree.xpath(self.xpath_relative)

            # Si pas trouvé, essayer avec le XPath absolu
            if not date_elements:
                date_elements = tree.xpath(self.xpath_absolute)

            # Si toujours pas trouvé, essayer avec la classe CSS
            if not date_elements:
                date_elements = tree.xpath(f'//p[@class="{self.class_selector}"]')

            # Si toujours pas trouvé, essayer une recherche plus large
            if not date_elements:
                date_elements = tree.xpath('//p[contains(@class, "text-date-blog")]')

            if date_elements:
                date_text = date_elements[0].text_content().strip()
                return date_text
            else:
                print("Aucun élément de date trouvé avec les XPath fournis")
                # Afficher quelques éléments p pour debug
                all_p_elements = tree.xpath('//p')
                print(f"Nombre total d'éléments <p> trouvés: {len(all_p_elements)}")
                for i, p in enumerate(all_p_elements[:10]):  # Afficher les 10 premiers
                    print(f"P[{i}]: class='{p.get('class', 'N/A')}', text='{p.text_content().strip()[:50]}...'")
                return None

        except Exception as e:
            print(f"Erreur lors du parsing HTML: {e}")
            return None

    def check_modification_date(self):
        """Vérifie et affiche la date de modification"""
        print(f"Vérification de la page: {self.url}")
        print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("-" * 60)

        html_content = self.fetch_page()
        if html_content is None:
            return None

        date_text = self.parse_date(html_content)
        if date_text:
            print(f"Date de modification trouvée: {date_text}")
            return date_text
        else:
            print("Aucune date de modification trouvée")
            return None


def main():
    url = "https://www.louveinvest.com/scpi/scpi-a-credit#Les-solutions-d-investissement-en-SCPI-a-credit-de-Louve-Invest"

    checker = PageModificationChecker(url)
    result = checker.check_modification_date()

    if result:
        print(f"\n✅ Succès: Date extraite = '{result}'")
    else:
        print(f"\n❌ Échec: Impossible d'extraire la date")
        sys.exit(1)


if __name__ == "__main__":
    main()